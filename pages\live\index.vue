<template>
	<view class="meet-square-container">
		<u-navbar :title="topNavStyle.title" :height="topNavStyle.height" :titleStyle='topNavStyle.Tstyle'
			:left-icon-color="topNavStyle.Lstyle" :bgColor="topNavStyle.style" :auto-back="false"
			:placeholder='true' left-icon="" style="z-index: 1;margin-top: -88upx;">
		</u-navbar>
		<z-paging :show-scrollbar="false" refresher-background='#f5f5f5' ref="paging" refresher-only :use-page-scroll='true' @onRefresh="onRefresh" >
			<!-- <view slot="top" class="custom-navbar" >
				<view class="live-btn" @click="goToPusher">
					<text class="live-btn-text">开始直播</text>
				</view>
			</view> -->
			
			<template #refresher="{refresherStatus}">
				<custom-refresher :status="refresherStatus" color="#333" />
			</template>
			
			<template #loadingMoreNoMore>
				<custom-nomore />
			</template>
			
			<!-- 顶部轮播图 - 始终显示，不受loading影响 -->
			<view class="banner-container" v-if="bannerList.length > 0">
				<swiper class="banner-swiper" :indicator-dots="true" :autoplay="true" interval="3000" duration="500" circular indicator-color="rgba(255,255,255,0.4)" indicator-active-color="#fff" :style="{height: bannerHeight + 'px'}">
					<swiper-item v-for="(item, index) in bannerList" :key="index">
						<image :src="item.imageUrl" mode="widthFix" class="banner-image" @click="handleBannerClick(item)" @load="onBannerImageLoad"></image>
					</swiper-item>
				</swiper>
			</view>
			
			<!-- 移除分类选项卡 -->
			
			<!-- 内容区域 - 根据加载状态显示骨架屏或内容 -->
			<view class="content-area">
				<!-- 骨架屏仅显示在内容区域 -->
				<view v-if="loading" class="skeleton-container">
					<view class="skeleton-item" v-for="i in 4" :key="i">
						<view class="skeleton-image"></view>
						<view class="skeleton-title"></view>
						<view class="skeleton-info"></view>
					</view>
				</view>
				
				<!-- 视频网格 -->
				<view v-else class="video-grid">
					<view 
						class="video-item" 
						v-for="(item, index) in videoList" 
						:key="index"
						@click="goToVideoDetail(item)"
					>
						<view class="video-cover">
							<image :src="item.videoThumbnailUrl" mode="widthFix"></image>
							<!-- 直播状态标识 -->
							<view class="video-status">
								<text :class="['status-text', getStatusClass(item.identification)]">
									{{getStatusText(item.identification)}}
								</text>
							</view>
						</view>
						<view class="video-content">
							<view class="video-title">{{ item.name }}</view>
							<view class="video-info">
								<view class="video-time">
									<u-icon name="clock" size="16" color="#999" class="info-icon"></u-icon>
									<text class="time-text">{{ formatStartTime(item.startTime) }}</text>
								</view>
								<!-- <view class="video-presenter" v-if="item.author">
									<u-icon name="account" size="24" color="#999" class="info-icon"></u-icon>
									<text class="presenter-text">{{ item.author }}</text>
								</view> -->
								<!-- <view class="video-presenter" v-else>
									<u-icon name="account" size="24" color="#999" class="info-icon"></u-icon>
									<text class="presenter-text">主讲老师</text>
								</view> -->
							</view>
						</view>
					</view>
					<!-- 空状态显示 -->
					<u-empty
						v-if="!loading && (!videoList || videoList.length === 0)"
						text="暂无内容"
						mode="list"
						icon="../../static/empty/list.png"
						margin-top="60"
					>
					</u-empty>
				</view>
			</view>
			<my-bottom></my-bottom>
			<u-toast ref="uToast"></u-toast>
			
			<!-- 直播未开始弹窗 -->
			<u-popup :show="showLiveNotStartedPopup" @close="closeLiveNotStartedPopup" mode="center" 
				:closeable="true" closeIconPos="top-right" round="16" :safeAreaInsetBottom='false'>
				<view class="live-not-started-popup">
					<view class="live-popup-header">
						<text class="live-popup-title">{{liveNotStartedInfo.toptitle}}</text>
					</view>
					<view class="live-popup-content">
						<image 
							:src="liveNotStartedInfo.coverImg" 
							mode="widthFix" 
							class="live-popup-cover"
						></image>
						<view class="live-popup-info">
							<view class="live-info-title">{{liveNotStartedInfo.title}}</view>
							<view class="live-info-meta">
								<u-icon name="account-fill" color="#666" size="20"></u-icon>
								<text class="live-info-author">{{liveNotStartedInfo.author}}</text>
							</view>
							<view class="live-info-meta">
								<u-icon name="clock-fill" color="#666" size="20"></u-icon>
								<text class="live-info-time">{{liveNotStartedInfo.startTime}}</text>
							</view>
						</view>
					</view>
					<view class="live-popup-status">
						
						<view class="live-status-text" v-if="liveNotStartedInfo.toptitle=='直播已结束'">直播已结束，敬请期待</view>
						<view class="live-status-text" v-else>直播尚未开始，请稍后再来</view>
					</view>
					<view class="live-popup-footer">
						<view class="live-popup-btn" @click="closeLiveNotStartedPopup">
							我知道了
						</view>
					</view>
				</view>
			</u-popup>
		</z-paging>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				loading: true,
				businessId: null, // 业务ID
				bannerList: [],
				videoList: [], // 视频列表
				page: 1, // 当前页码
				pageSize: 10, // 每页数量
				scrollTop: 0,
				companyId: '', // 添加companyId
				bannerHeight: 160, // 轮播图高度初始值
				
				// 添加直播相关变量
				currentPageNum: 1,
				liveData: [],
				showLiveNotStartedPopup: false,
				liveNotStartedInfo: {
					toptitle: '',
					title: '',
					coverImg: '',
					startTime: '',
					author: ''
				}
			}
		},
		computed: {
			topNavStyle() {
				let r = this.scrollTop / 100
				return {
					style: `rgba(255,255,255,${r>=1?1:r})`,
					Tstyle: `color:${r>1?'#000':'#fff'}`,
					Lstyle: r > 1 ? '#000' : '#fff',
					title: r > 1 ? '在线直播' : '',
					height: r > 1 ? '44' : '44',
				}
			}
		},
		onLoad(options) {
			console.log(options)
			
			// 初始化公司ID
			// this.initCompanyId();
			
		
		},
		onShow() {
			// 获取顶部轮播图
			this.getAdvertisingData();
			
			// 获取直播列表数据
			this.getLivePageData();
		},
		onPageScroll(e){
			if (e) {
				this.scrollTop = e.scrollTop;
			}
		},
		methods: {
			// 初始化公司ID
			initCompanyId() {
				// 从全局或缓存中获取公司ID
				const app = getApp();
				if (app.globalData && app.globalData.companyId) {
					this.companyId = app.globalData.companyId;
				} else {
					// 如果没有全局ID，尝试从缓存获取
					const cachedCompanyId = uni.getStorageSync('companyId');
					if (cachedCompanyId) {
						this.companyId = cachedCompanyId;
					}
					
					// 如果全局数据对象不存在，初始化它
					if (!app.globalData) {
						app.globalData = {};
					}
					
					// 更新全局公司ID
					app.globalData.companyId = this.companyId;
				}
				
				console.log('当前使用的公司ID:', this.companyId);
			},
			
			// 获取顶部广告轮播图
			getAdvertisingData() {
				this.syghttp.ajax({
					url: this.syghttp.api.getAdvertising,
					method: 'POST',
					data: {
						"layoutPosition": "APP_LiveTop",
						"advertisingSize": 10
					},
					success: (res) => {
						console.log('轮播图数据返回:', res);
						if (res.code == 1000) {
							if (res.data && res.data.advertisings && Array.isArray(res.data.advertisings)) {
								this.bannerList = res.data.advertisings.map(item => {
									return {
										id: item.id || '',
										imageUrl: item.fileUrl || '',
										linkUrl: item.dataId || '',
										title: item.tittle || ''
									};
								});
								console.log('处理后的轮播图数据:', this.bannerList);
							}
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							});
						}
					},
					fail: () => {
						uni.showToast({
							title: '获取轮播图数据失败',
							icon: 'none'
						});
					}
				});
			},
			
			// 获取直播页面数据
			getLivePageData() {
				this.loading = true;
				
				this.syghttp.ajax({
					url: this.syghttp.api.getLivePage,
					method: 'POST',
					data: {
						"page": {
							"maxResultCount": 0,
							"pageNo": this.currentPageNum,
							"pageSize": 10,
							"skipCount": 0
						}
					},
					success: (res) => {
						this.loading = false;
						console.log('直播数据返回:', res);
						
						if (res.code == 1000) {
							if (res.data && res.data.items && res.data.items.items) {
								const liveItems = res.data.items.items;
								this.videoList = liveItems.map(item => {
									return {
										id: item.id || '',
										videoThumbnailUrl: item.liveCover || '',
										liveUrl: item.liveUrl || '',
										name: item.tittle || '',
										duration: item.duration || 0,
										identification: item.identification || 0,
										startTime: item.startTime || '',
										author: item.nickName || '未知',
										contentType: item.contentType,
										playWay: item.playWay || 0
									};
								});
							}
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							});
						}
						
						// 完成下拉刷新
						if (this.$refs.paging) {
							this.$refs.paging.complete();
						}
					},
					fail: () => {
						this.loading = false;
						uni.showToast({
							title: '获取直播数据失败',
							icon: 'none'
						});
						
						// 完成下拉刷新
						if (this.$refs.paging) {
							this.$refs.paging.complete();
						}
					},
					complete: () => {
						// 确保在任何情况下都会执行complete
						this.loading = false;
						if (this.$refs.paging) {
							this.$refs.paging.complete();
						}
					}
				});
			},
			
			// 轮播图点击事件
			handleBannerClick(item) {
				if (item.linkUrl) {
					// 如果有链接，则跳转到对应页面
					uni.navigateTo({
						url: item.linkUrl
					});
				}
			},
			
			// 获取直播状态对应的文字
			getStatusText(identification) {
				switch(identification) {
					case 1: return '直播中';
					case 2: return '回放';
					default: return '预告';
				}
			},
			
			// 获取直播状态对应的样式类
			getStatusClass(identification) {
				switch(identification) {
					case 1: return 'status-living';
					case 2: return 'status-playback';
					default: return 'status-preview';
				}
			},
			
			// 格式化开始时间
			formatStartTime(timeStr) {
				if (!timeStr) return '时间待定';
				
				try {
					const date = new Date(timeStr);
					const now = new Date();
					const year = date.getFullYear();
					const month = (date.getMonth() + 1).toString().padStart(2, '0');
					const day = date.getDate().toString().padStart(2, '0');
					const hour = date.getHours().toString().padStart(2, '0');
					const minute = date.getMinutes().toString().padStart(2, '0');
					
					// 如果是今年的日期，则不显示年份
					if (year === now.getFullYear()) {
						return `${month}月${day}日 ${hour}:${minute}`;
					}
					
					return `${year}年${month}月${day}日 ${hour}:${minute}`;
				} catch (e) {
					console.error('日期格式化错误:', e);
					return '时间待定';
				}
			},
			
			// 格式化视频时长 (保留，但不再使用)
			formatDuration(seconds) {
				if (!seconds) return '00:00';
				
				const minutes = Math.floor(seconds / 60);
				const remainingSeconds = Math.floor(seconds % 60);
				
				return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
			},
			
			// 跳转到视频详情页
			goToVideoDetail(item) {
				console.log('点击视频项:', item);
				
				// 判断是否是直播 (identification == 1 表示正在直播)
				if (item.identification === 1) {
					// 正在直播，获取直播链接并跳转
					this.getLivePlayUrl({
						id: item.id,
						name: item.name || item.tittle,
						liveUrl: item.liveUrl,
						startTime: item.startTime,
						author: item.author || item.nickName || '主讲老师',
						playWay: item.playWay || 0,
						duration: item.duration,
						videoThumbnailUrl: item.videoThumbnailUrl || item.liveCover,
						contentType: item.contentType
					});
				} else if (item.identification === 2) {
					// 回看，可以直接跳转到视频播放页
					uni.navigateTo({
						url: `/pages/live/video?id=${item.id}`
					});
				} else {
					// 预告，显示未开始弹窗
					this.showLiveNotStartedPopup = true;
					this.liveNotStartedInfo = {
						toptitle: '直播未开始',
						title: item.name || item.tittle || '直播预告',
						coverImg: item.videoThumbnailUrl || item.liveCover || '/static/empty/2.png',
						startTime: item.startTime ? this.formatStartTime(item.startTime) : '时间待定',
						author: item.author || item.nickName || '主讲老师'
					};
				}
			},
			
			// 获取直播播放URL
			getLivePlayUrl(liveData) {
				// 判断直播是否已结束（比较当前时间与直播时长）
				const currentTime = new Date().getTime();
				const startTime = new Date(liveData.startTime).getTime();
				const duration = liveData.duration || 0; // 直播时长（秒）
				const endTime = startTime + (duration * 1000); // 转换为毫秒
				
				const isLiveEnded = duration > 0 && currentTime > endTime;
				
				// 对于contentType不为2的直播，通过API获取直播地址
				if (liveData.contentType !== 2) {
					// 调用API获取直播播放地址
					this.syghttp.ajax({
						url: this.syghttp.api.getLivePlay,
						method: 'GET',
						data: {
							dataId: liveData.id,
							companyId: this.companyId
						},
						success: (res) => {
							if (res.code === 1000 && res.data) {
								// 根据设备类型选择合适的直播流格式
								let liveUrl = '';
								
								// 使用uni API检测设备类型
								const systemInfo = uni.getSystemInfoSync();
								const platform = systemInfo.platform.toLowerCase();
								const isAppleDevice = platform === 'ios';
								
								if (isAppleDevice) {
									// 苹果设备优先使用HLS格式
									if (res.data.HLS) {
										console.log('苹果设备使用HLS格式');
										liveUrl = res.data.HLS;
									} else {
										// 苹果设备不支持FLV，回退到其他格式
										liveUrl = res.data.RTMP || liveData.liveUrl;
									}
								} else {
									// 安卓设备优先使用FLV格式
									if (res.data.FLV) {
										console.log('安卓设备使用FLV格式');
										liveUrl = res.data.FLV;
									} else if (res.data.HLS) {
										liveUrl = res.data.HLS;
									} else if (res.data.RTMP) {
										liveUrl = res.data.RTMP;
									} else {
										// 如果API没返回流地址，尝试使用原有地址
										liveUrl = liveData.liveUrl;
									}
								}
								
								console.log('获取到的直播URL:', liveUrl);
								if (!liveUrl) {
									this.$refs.uToast.show({
										type: 'error',
										message: '无法获取直播地址'
									});
									return;
								}
								
								// 准备传递给直播页面的视频数据
								const videoData = {
									url: liveUrl,
									id: liveData.id,
									name: liveData.name || '精彩直播',
									author: liveData.author || '未知',
									startTime: liveData.startTime,
									playWay: liveData.playWay || 0,
									videoThumbnailUrl: liveData.videoThumbnailUrl || '',
									type: liveData.contentType
								};
								
								console.log('传递给直播页面的视频数据:', videoData);
								
								// 跳转到视频播放页
								uni.navigateTo({
									url: `/pages/investment/live?id=${liveData.id}&videoData=${encodeURIComponent(JSON.stringify(videoData))}`,
									success: () => {
										console.log('成功跳转到直播页面');
									},
									fail: (err) => {
										console.error('跳转到直播页面失败:', err);
										this.$refs.uToast.show({
											type: 'error',
											message: '打开直播页面失败'
										});
									}
								});
							} else {
								this.$refs.uToast.show({
									type: 'error',
									message: '获取直播信息失败'
								});
							}
						},
						fail: (err) => {
							console.error('获取直播播放地址失败:', err);
							this.$refs.uToast.show({
								type: 'error',
								message: '获取直播信息失败'
							});
						}
					});
				} else {
					// contentType为2的直播
					// 先判断直播是否已结束
					if (isLiveEnded) {
						// 更新弹窗信息
						this.liveNotStartedInfo = {
							toptitle: '直播已结束',
							title: liveData.name || '直播',
							coverImg: liveData.videoThumbnailUrl || '/static/empty/2.png',
							startTime: liveData.startTime ? this.formatStartTime(liveData.startTime) : '已结束',
							author: liveData.author || '主讲老师'
						};
						// 显示自定义弹窗
						this.showLiveNotStartedPopup = true;
						return;
					}
					
					// 直播未结束，使用liveUrl直接跳转
					if (liveData.liveUrl) {
						// 计算当前视频应该播放的进度（秒）
						let currentProgress = 0;
						
						if (liveData.startTime) {
							// 获取当前时间和开始时间的时间差（毫秒）
							const currentTime = new Date().getTime();
							const startTime = new Date(liveData.startTime).getTime();
							const timeElapsed = currentTime - startTime;
							
							// 将时间差转换为秒
							const secondsElapsed = Math.floor(timeElapsed / 1000);
							
							// 如果设置了视频时长，确保播放进度不超过视频总时长
							if (liveData.duration && liveData.duration > 0) {
								currentProgress = Math.min(secondsElapsed, liveData.duration);
							} else {
								currentProgress = secondsElapsed;
							}
							
							console.log('计算的视频播放进度:', currentProgress, '秒');
						}
						
						// 准备传递给直播页面的视频数据
						const videoData = {
							url: liveData.liveUrl,
							id: liveData.id,
							name: liveData.name || '精彩直播',
							author: liveData.author || '未知',
							startTime: liveData.startTime,
							playWay: liveData.playWay || 0,
							videoThumbnailUrl: liveData.videoThumbnailUrl || '',
							currentProgress: currentProgress,
							type: liveData.contentType
						};
						
						console.log('传递给直播页面的视频数据:', videoData);
						
						// 跳转到视频播放页
						uni.navigateTo({
							url: `/pages/investment/live?id=${liveData.id}&videoData=${encodeURIComponent(JSON.stringify(videoData))}`,
							success: () => {
								console.log('成功跳转到直播页面');
							},
							fail: (err) => {
								console.error('跳转到直播页面失败:', err);
								this.$refs.uToast.show({
									type: 'error',
									message: '打开直播页面失败'
								});
							}
						});
					} else {
						// 没有直播URL
						this.$refs.uToast.show({
							type: 'error',
							message: '无法获取直播地址'
						});
					}
				}
			},
			
			// 关闭直播未开始弹窗
			closeLiveNotStartedPopup() {
				this.showLiveNotStartedPopup = false;
			},
			
			// 下拉刷新
			onRefresh() {
				// 重新加载轮播图数据
				this.getAdvertisingData();
				
				// 重置页码
				this.currentPageNum = 1;
				
				// 刷新数据
				this.getLivePageData();
			},

			// 轮播图图片加载完成回调
			onBannerImageLoad(e) {
				// 获取图片高度
				const height = e.detail.height;
				// 设置轮播图高度
				// this.bannerHeight = height;
			},
			goToPusher() {
				uni.navigateTo({
					url: '/pages/live/live-pusher'
				});
			}
		}
	}
</script>

<style>
	page, body {
		background-color: #f5f5f5;
	}
</style>

<style lang="scss" scoped>
	.meet-square-container {
		background-color: #f8f8f8;
		min-height: 100vh;
	}
	
	.custom-navbar {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 999;
		transition: opacity 0.3s;
	}
	
	.banner-container {
		width: 100%;
		height: auto;
		padding: 24rpx;
		box-sizing: border-box;
	}
	
	.banner-swiper {
		width: 100%;
		height: auto;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
	}
	
	.banner-image {
		width: 100%;
		display: block;
		border-radius: 16rpx;
	}
	
	.content-area {
		min-height: 500rpx;
		padding: 10rpx 20rpx 30rpx;
	}
	
	/* 骨架屏样式 */
	.skeleton-container {
		display: flex;
		flex-wrap: wrap;
		padding: 20rpx;
		justify-content: space-between;
	}
	
	.skeleton-item {
		width: 48%;
		margin-bottom: 30rpx;
		background-color: #ffffff;
		border-radius: 16rpx;
		overflow: hidden;
		padding-bottom: 15rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
	}
	
	.skeleton-image {
		width: 100%;
		height: 240rpx;
		background-color: #f2f2f2;
		animation: skeleton-blink 1.5s infinite;
	}
	
	.skeleton-title {
		margin: 15rpx;
		height: 30rpx;
		background-color: #f2f2f2;
		animation: skeleton-blink 1.5s infinite;
		width: 90%;
		border-radius: 4rpx;
	}
	
	.skeleton-info {
		margin: 15rpx;
		height: 30rpx;
		background-color: #f2f2f2;
		animation: skeleton-blink 1.5s infinite;
		width: 40%;
		border-radius: 4rpx;
	}
	
	@keyframes skeleton-blink {
		0% {
			opacity: 0.5;
		}
		50% {
			opacity: 1;
		}
		100% {
			opacity: 0.5;
		}
	}
	
	.video-grid {
		display: flex;
		justify-content: space-between;
		flex-wrap: wrap;
		padding: 10rpx 0;
	}
	
	.video-item {
		width: 345rpx;
		margin-bottom: 30rpx;
		background-color: #FFFFFF;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
		transition: all 0.3s;
	}
	
	.video-item:active {
		transform: scale(0.97);
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
	}
	
	.video-cover {
		width: 100%;
		overflow: hidden;
		position: relative;
		border-radius: 16rpx 16rpx 0 0;
		
		image {
			width: 100%;
			display: block;
		}
		
		/* 直播状态样式 */
		.video-status {
			position: absolute;
			right: 10rpx;
			top: 16rpx;
			padding: 4rpx 12rpx;
			border-radius: 6rpx;
		}
		
		.status-text {
			font-size: 22rpx;
			color: #FFFFFF;
			padding: 6rpx 16rpx;
			border-radius: 6rpx;
			font-weight: 500;
			box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
		}
		
		.status-living {
			background: linear-gradient(135deg, #FF4D4F, #FF7875);
		}
		
		.status-playback {
			background: linear-gradient(135deg, #00C1CC, #13C2C2);
		}
		
		.status-preview {
			background: linear-gradient(135deg, #FFC107, #FFEC3D);
		}
	}
	
	.video-content {
		padding: 20rpx;
	}
	
	.video-title {
		font-size: 28rpx;
		color: #303133;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		min-height: 80rpx;
		font-weight: 500;
		line-height: 1.4;
	}
	
	.video-info {
		display: flex;
		flex-direction: column;
		margin-top: 16rpx;
	}
	
	.video-time, .video-presenter {
		display: flex;
		align-items: center;
		font-size: 24rpx;
		color: #909399;
		margin-bottom: 8rpx;
	}
	
	.info-icon {
		margin-right: 8rpx;
	}
	
	.time-text, .presenter-text {
		margin-left: 8rpx;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		max-width: 260rpx;
	}
	
	/* 直播未开始弹窗样式 */
	.live-not-started-popup {
		width: 600rpx;
		background-color: #fff;
		border-radius: 20rpx;
		overflow: hidden;
	}
	
	.live-popup-header {
		background: linear-gradient(135deg, #00C1CC, #0099FF);
		padding: 30rpx;
		text-align: center;
	}
	
	.live-popup-title {
		color: #fff;
		font-size: 32rpx;
		font-weight: bold;
	}
	
	.live-popup-content {
		padding: 30rpx;
	}
	
	.live-popup-cover {
		width: 100%;
		border-radius: 16rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
	}
	
	.live-popup-info {
		padding: 10rpx 0;
	}
	
	.live-info-title {
		font-size: 30rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 16rpx;
	}
	
	.live-info-meta {
		display: flex;
		align-items: center;
		margin-bottom: 10rpx;
	}
	
	.live-info-author, .live-info-time {
		margin-left: 10rpx;
		font-size: 24rpx;
		color: #666;
	}
	
	.live-popup-status {
		background-color: #F8F8F8;
		padding: 20rpx 30rpx;
		display: flex;
		align-items: center;
		border-top: 1px solid #EEEEEE;
		border-bottom: 1px solid #EEEEEE;
	}
	
	.live-status-text {
		margin-left: 20rpx;
		font-size: 26rpx;
		color: #FF5F5F;
	}
	
	.live-popup-footer {
		padding: 30rpx;
		display: flex;
		justify-content: center;
	}
	
	.live-popup-btn {
		width: 80%;
		height: 80rpx;
		background: linear-gradient(135deg, #00C1CC, #0099FF);
		border-radius: 40rpx;
		color: #fff;
		font-size: 28rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 8rpx 16rpx rgba(0, 193, 204, 0.3);
		transition: transform 0.2s ease;
	}
	
	.live-popup-btn:active {
		transform: scale(0.96);
	}
	.live-btn {
  position: absolute;
  right: 30rpx;
  top: 30rpx;
  background-color: #FF5722;
  padding: 15rpx 30rpx;
  border-radius: 40rpx;
  z-index: 999;
}
.live-btn-text {
  color: #FFFFFF;
  font-size: 28rpx;
  font-weight: bold;
}
</style>