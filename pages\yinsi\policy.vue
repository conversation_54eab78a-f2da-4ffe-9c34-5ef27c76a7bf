<template>
	<view class="policy-container">
		<u-navbar :title="pageTitle" :auto-back="true" bgColor="#fff" :placeholder="true"
			style="box-shadow: 0rpx 2rpx 10rpx rgba(0,0,0,0.11);">
		</u-navbar>
		<view v-if="loading">
			<u-skeleton rows="20" title loading></u-skeleton>
		</view>
		<l-watermark :content="['生意港', '版权所有,禁止转载']" v-else :baseSize="2"   :font="{'fontSize':'14'}" >
			<view class="content-container">
			
				<view  class="policy-content">
					<!-- <view class="title">{{content.title}}</view> -->
					<u-parse :content="content" @load="handleParseLoad"></u-parse>
				</view>
				<!-- 注销按钮 -->
				<view class="delete-account" v-if="options.delete">
					<u-button type="error" text="注销账号" @click="handleDeleteAccount" style="height: 100upx;"></u-button>
				</view>
			</view>
		</l-watermark>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				loading: true,
				type: 1, // 默认服务协议
				content: {
					title: '',
					content: ''
				},
				typeMap: {
					1: '用户协议',
					2: '隐私政策',
					3: '服务条款',
					4: '运营协议',
					5:'支付协议',
					99:'注销协议'
				},
				options: {
					delete: false
				}
			}
		},
		computed: {
			pageTitle() {
				console.log(this.typeMap[this.type])
				return this.typeMap[this.type] || '协议内容'
			}
		},
		onLoad(options) {
			if (options.type) {
				this.type = Number(options.type)
			}
			if (options.delete) {
				this.options.delete = options.delete === 'true'
			}
			this.getContent()
		},
		methods: {
			// 获取协议列表
			async getPublishedList() {
				try {
					const res = await this.syghttp.ajax({
						url: this.syghttp.api.getPublishedList,
						method: 'GET',
						data: {type: this.type}
					})

					if (res.code === 1000) {
						// 根据返回的嵌套数据结构获取items数组
						return res.data?.items?.items || []
					} else {
						this.showError(res.msg || '获取协议列表失败')
						return []
					}
				} catch (err) {
					this.showError('获取协议列表失败')
					console.error('获取协议列表失败:', err)
					return []
				}
			},

			// 获取内容
			async getContent() {
				try {
					// 先获取协议列表
					const protocolList = await this.getPublishedList()
					
					if (!protocolList.length) {
						this.showError('未找到对应的协议内容')
						this.loading = false
						return
					}
					
					// 获取列表中的第一条数据的id
					const protocolId = protocolList[0].id
					
					// 使用协议项的id获取详情
					const res = await this.syghttp.ajax({
						url: this.syghttp.api.protocoldetail,
						method: 'GET',
						data: {
							id: protocolId
						}
					})

					if (res.code === 1000) {
						// 如果返回的是数组，取第一条
							this.content = res.data.content || {}
							console.log(this.content)
					} else {
						this.showError(res.msg || '获取内容失败')
					}
				} catch (err) {
					this.showError('获取内容失败')
					console.error('获取内容失败:', err)
				} finally {
					setTimeout(() => {
						this.loading = false
					}, 1000)
				}
			},

			// 错误提示
			showError(message) {
				uni.showToast({
					title: message,
					icon: 'none'
				})
			},

			// 处理富文本加载完成事件
			handleParseLoad() {
				// #ifdef H5
				setTimeout(() => {
					// 使用动态样式表来设置图片样式
					const style = document.createElement('style')
					style.textContent = `
						.content-container img {
							-webkit-user-select: none !important;
							-moz-user-select: none !important;
							-ms-user-select: none !important;
							user-select: none !important;
							pointer-events: none !important;
							-webkit-touch-callout: none !important;
							touch-action: none !important;
						}
					`
					document.head.appendChild(style)
					
					let touchStartTime = 0
					const LONG_PRESS_DURATION = 500 // 长按判定时间（毫秒）
					
					// 触摸开始
					const handleTouchStart = () => {
						touchStartTime = Date.now()
					}
					
					// 触摸结束
					const handleTouchEnd = (e) => {
						const touchDuration = Date.now() - touchStartTime
						if (touchDuration >= LONG_PRESS_DURATION) {
							e.preventDefault()
							uni.showToast({
								title: '禁止保存',
								icon: 'none'
							})
						}
					}
					
					// 右键菜单
					const handleContextMenu = (e) => {
						e.preventDefault()
						uni.showToast({
							title: '禁止保存',
							icon: 'none'
						})
						return false
					}
					
					const container = document.querySelector('.content-container')
					if (container) {
						container.addEventListener('touchstart', handleTouchStart, { passive: true })
						container.addEventListener('touchend', handleTouchEnd, { passive: false })
						container.addEventListener('contextmenu', handleContextMenu, true)
					}
				}, 500)
				// #endif
				
				// #ifdef APP-PLUS
				setTimeout(() => {
					const pages = getCurrentPages()
					const page = pages[pages.length - 1]
					const currentWebview = page.$getAppWebview()
					
					currentWebview.evalJS(`
						(function() {
							// 添加样式
							const style = document.createElement('style');
							style.textContent = \`
								img {
									-webkit-user-select: none !important;
									-moz-user-select: none !important;
									-ms-user-select: none !important;
									user-select: none !important;
									pointer-events: none !important;
									-webkit-touch-callout: none !important;
									touch-action: none !important;
								}
							\`;
							document.head.appendChild(style);
							
							let touchStartTime = 0;
							const LONG_PRESS_DURATION = 500;
							
							// 触摸开始
							function handleTouchStart() {
								touchStartTime = Date.now();
							}
							
							// 触摸结束
							function handleTouchEnd(e) {
								const touchDuration = Date.now() - touchStartTime;
								if (touchDuration >= LONG_PRESS_DURATION) {
									e.preventDefault();
									plus.nativeUI.toast('禁止保存');
								}
							}
							
							// 右键菜单
							function handleContextMenu(e) {
								e.preventDefault();
								plus.nativeUI.toast('禁止保存');
								return false;
							}
							
							document.body.addEventListener('touchstart', handleTouchStart, { passive: true });
							document.body.addEventListener('touchend', handleTouchEnd, { passive: false });
							document.body.addEventListener('contextmenu', handleContextMenu, true);
						})();
					`)
				}, 500)
				// #endif
			},
			// 处理注销账号
			handleDeleteAccount() {
				uni.showModal({
					title: '注销确认',
					content: '确定要注销账号吗？注销后账号将无法恢复！',
					confirmText: '确认注销',
					confirmColor: '#f56c6c',
					success: async (res) => {
						if (res.confirm) {
							try {
								const res = await this.syghttp.ajax({
									url: this.syghttp.api.deleteMember,
									method: 'POST'
								})
								
								if (res.code === 1000) {
									uni.showToast({
										title: '账号已注销',
										icon: 'success'
									})
									// 清除本地存储
									getApp().globalData.login=false
									uni.clearStorageSync()
									// 延迟跳转到登录页
									setTimeout(() => {
										uni.reLaunch({
											url: '/pages/sign/sign'
										})
									}, 1500)
								} else {
									this.showError(res.msg || '注销失败')
								}
							} catch (err) {
								this.showError('注销失败')
								console.error('注销失败:', err)
							}
						}
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.policy-container {
		height: 100%;
		background-color: #fff;
	}

	.content-container {
		padding: 30rpx;

		.policy-content {
			min-height: 100%;
			.title {
				font-size: 36rpx;
				font-weight: bold;
				color: #333;
				margin-bottom: 30rpx;
				text-align: center;
			}

			/deep/ rich-text {
				font-size: 28rpx;
				line-height: 1.8;
				color: #666;

				view,
				p {
					margin-bottom: 20rpx;
				}

				image {
					max-width: 100%;
					height: auto;
				}
			}
		}

		.delete-account {
			margin-top: 60rpx;
			padding: 0 30rpx;
			margin-bottom: 40rpx;
		}

		/deep/ {
			img {
				pointer-events: none;
				-webkit-touch-callout: none;
				user-select: none;
			}
		}
	}
</style>